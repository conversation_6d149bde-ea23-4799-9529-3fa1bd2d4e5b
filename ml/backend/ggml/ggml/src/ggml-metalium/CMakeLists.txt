find_package(tt-nn REQUIRED)

message(STATUS "Metalium/TTNN found.")

ggml_add_backend_library(ggml-metalium
    ggml-metalium.cpp
)

target_include_directories(ggml-metalium PRIVATE
    # Parent GGML stuff
    ..
)

target_link_directories(ggml-metalium PRIVATE
    $ENV{TT_METAL_HOME}/build/ttnn
)

target_link_libraries(ggml-metalium PRIVATE
    TT::Metalium
    TTNN::TTNN
    ggml-base
    #ggml-cpu
)

if(NOT GGML_BACKEND_DL)
    list(APPEND METALIUM_LINK_LIBS ggml-cpu)
else()
    # When using dynamic loading, we need to handle CPU functionality differently
    # Either include CPU sources directly or use a different approach
    message(WARNING "ggml-cpu cannot be linked when GGML_BACKEND_DL is enabled. CPU functionality may be limited.")
endif()

target_compile_definitions(
    ggml-metalium PRIVATE
    FMT_HEADER_ONLY
    SPDLOG_FMT_EXTERNAL
)

if(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsized-deallocation")
endif()

target_precompile_headers(ggml-metalium PRIVATE metalium-pch.hpp)
target_compile_features(ggml-metalium PRIVATE cxx_std_20)
